package consts

type UserLoginType int //	用户登录类型
type UserLoginRole int //	用户登录角色

// 用户登录类型
const (
	LoginType_UnKnow = 0 //	未知登录类型
	LoginType_App    = 1 //	手机端登录
	LoginType_Web    = 2 //	网页端登录
	LoginType_PcApp  = 3 //	电脑端app登录
)

// 用户登录类型对应的字符串
var loginTypMap = map[int]string{
	LoginType_UnKnow: "Login_UnKnow",
	LoginType_App:    "Login_App",
	LoginType_Web:    "Login_Web",
	LoginType_PcApp:  "Login_PcApp",
}

// 获取登录类型字符串
func LoginTypeToString(_nType int) string {
	if _nType >= 0 && _nType < len(loginTypMap) {
		return loginTypMap[_nType]
	}
	return "Login_UnKnow"
}

// 群加入方式
const (
	GroupJoinType_Freedom  = 1 //	自由加入
	GroupJoinType_Validate = 2 //	需要验证
	GroupJoinType_Forbid   = 3 //	需要验证
)

// 好友处理状态
const (
	FriendHandlrStatus_Pending = 1 //	待处理
	FriendHandlrStatus_agree   = 2 //	同意
	FriendHandlrStatus_refuse  = 3 //	拒绝
	FriendHandlrStatus_ignore  = 4 //	忽略
)

// 接收者类型常量
const (
	ReceiverType_Private = 1 // 单聊
	ReceiverType_Group   = 2 // 群聊
)

// 聊天会话状态 1正常，2置顶，3免打扰，4删除
const (
	ChatConvStatus_Normal = 1 //	正常
	ChatConvStatus_Top    = 2 //	置顶
	ChatConvStatus_NoTips = 3 //	免打扰
	ChatConvStatus_Del    = 4 //	删除
)

// 主消息类型常量
const (
	MsgType_Chat       = "chat"        // 普通聊天消息
	MsgType_ChatNotice = "chat_notice" // 通知消息（如好友申请、群邀请等）
	MsgType_System     = "system"      // 系统消息
)

// 子消息类型
const (
	//	chat 聊天类型
	ConntetType_Text      = "text"       //	文本消息
	ConntetType_Image     = "image"      //	图片
	ConntetType_Audio     = "audio"      //	音频
	ConntetType_Video     = "video"      //	视频
	ConntetType_File      = "file"       //	文件
	ConntetType_VideoCall = "video_call" //	视频通话
	ConntetType_AudioCall = "audio_call" //	音频通话

	//	chat_notice 类型
	ConntetType_AddFriendOk     = "add_friend_ok"     //	添加好友
	ConntetType_MsgStatusUpdate = "msg_status_update" //	聊天信息状态更新
)

// 获取消息状态提示
func GetMsgStatusTips(nMsgStatus int8) string {
	msgStatus, ok := MessageStatusTipsMap[nMsgStatus]
	if ok {
		return msgStatus
	}

	return "UnKnow"
}

var MessageStatusTipsMap = map[int8]string{
	MessageStatus_Sending:   "发送中",
	MessageStatus_Sended:    "已发送",
	MessageStatus_Delivered: "已送达,未读",
	MessageStatus_Readed:    "已读",

	MessageStatus_HasDelete:   "您已删除好友",
	MessageStatus_NotFriend:   "对方不是您的好友",
	MessageStatus_BeenBlocked: "您已被对方拉黑",
	MessageStatus_BeenDeleted: "您已被对方删除好友",

	MessageStatus_GroupMuted:     "您已禁言",
	MessageStatus_NotInGroup:     "您不是该群成员",
	MessageStatus_GroupRemove:    "您已被移出该群组",
	MessageStatus_NotGroupMember: "您未加入该群组",
	MessageStatus_GroupExit:      "您已退出该群组",

	MessageStatus_SysError: "系统错误",
}

// 聊天消息状态
const (
	//	正常状态
	MessageStatus_Sending   = 1 //	发送中
	MessageStatus_Sended    = 2 //	已发送
	MessageStatus_Delivered = 3 //	已送达
	MessageStatus_Readed    = 4 //	已读

	//	单聊异常
	MessageStatus_HasDelete   = 11 //	您已删除好友
	MessageStatus_NotFriend   = 12 //	对方不是您的好友
	MessageStatus_BeenBlocked = 13 //	您已被对方拉黑
	MessageStatus_BeenDeleted = 14 //	您已被对方删除好友

	//	群聊异常
	MessageStatus_GroupMuted     = 16 //	您已禁言
	MessageStatus_GroupRemove    = 17 //	您已不是该群成员
	MessageStatus_NotInGroup     = 18 //	您不是该群成员
	MessageStatus_NotGroupMember = 19 //	您未加入该群组
	MessageStatus_GroupExit      = 20 //	您已退出该群组

	//	系统错误
	MessageStatus_SysError = 21 //	系统错误
)

// 聊天群角色
const (
	ChatGroupRole_Comm  = 0 //	普通用户
	ChatGroupRole_Admin = 1 //	管理岗
	ChatGroupRole_Owner = 2 //	群主
)

// 消息推送策略
const (
	// 1、基础推送策略
	PushStrategy_All      = 1 // 全部推送（默认）- 单聊双方都推送，群聊所有成员都推送
	PushStrategy_Sender   = 2 // 仅发送方 - 只推送给消息发送者
	PushStrategy_Receiver = 3 // 仅接收方 - 只推送给消息接收者（单聊）或群成员（群聊，排除发送者）
	PushStrategy_Custom   = 4 // 指定用户 - 推送给PushTargets指定的用户列表

	// 2、群聊专用推送策略
	PushStrategy_GroupAdmin = 5 // 群管理员 - 只推送给群管理员（包括群主）
	PushStrategy_GroupOwner = 6 // 群所有者 - 只推送给群主

	// 3、扩展推送策略
	PushStrategy_Online     = 7  // 仅在线用户 - 只推送给当前在线的目标用户
	PushStrategy_Offline    = 8  // 仅离线用户 - 只推送给当前离线的目标用户（用于离线推送）
	PushStrategy_NoSender   = 9  // 排除发送者 - 推送给除发送者外的所有目标用户
	PushStrategy_SystemOnly = 10 // 系统消息 - 特殊系统消息推送策略
)

// 消息推送策略对应的字符串描述
var PushStrategyMap = map[int8]string{
	PushStrategy_All:        "全部推送",
	PushStrategy_Sender:     "仅发送方",
	PushStrategy_Receiver:   "仅接收方",
	PushStrategy_Custom:     "指定用户",
	PushStrategy_GroupAdmin: "群管理员",
	PushStrategy_GroupOwner: "群所有者",
	PushStrategy_Online:     "仅在线用户",
	PushStrategy_Offline:    "仅离线用户",
	PushStrategy_NoSender:   "排除发送者",
	PushStrategy_SystemOnly: "系统消息",
}

// 获取推送策略描述
func GetPushStrategyDesc(strategy int8) string {
	if desc, ok := PushStrategyMap[strategy]; ok {
		return desc
	}
	return "未知策略"
}
