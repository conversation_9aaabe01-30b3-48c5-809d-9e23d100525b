package consts

type UserLoginType int //	用户登录类型
type UserLoginRole int //	用户登录角色

// 用户登录类型
const (
	LoginType_UnKnow = 0 //	未知登录类型
	LoginType_App    = 1 //	手机端登录
	LoginType_Web    = 2 //	网页端登录
	LoginType_PcApp  = 3 //	电脑端app登录
)

// 用户登录类型对应的字符串
var loginTypMap = map[int]string{
	LoginType_UnKnow: "Login_UnKnow",
	LoginType_App:    "Login_App",
	LoginType_Web:    "Login_Web",
	LoginType_PcApp:  "Login_PcApp",
}

// 获取登录类型字符串
func LoginTypeToString(_nType int) string {
	if _nType >= 0 && _nType < len(loginTypMap) {
		return loginTypMap[_nType]
	}
	return "Login_UnKnow"
}

// 群加入方式
const (
	GroupJoinType_Freedom  = 1 //	自由加入
	GroupJoinType_Validate = 2 //	需要验证
	GroupJoinType_Forbid   = 3 //	需要验证
)

// 好友处理状态
const (
	FriendHandlrStatus_Pending = 1 //	待处理
	FriendHandlrStatus_agree   = 2 //	同意
	FriendHandlrStatus_refuse  = 3 //	拒绝
	FriendHandlrStatus_ignore  = 4 //	忽略
)

// 聊天类型
const (
	ChatMsgType_Private = 1 //	单聊
	ChatMsgType_Group   = 2 //	群聊
)

// 聊天会话状态 1正常，2置顶，3免打扰，4删除
const (
	ChatConvStatus_Normal = 1 //	正常
	ChatConvStatus_Top    = 2 //	置顶
	ChatConvStatus_NoTips = 3 //	免打扰
	ChatConvStatus_Del    = 4 //	删除
)

// 获取消息状态提示
func GetMsgStatusTips(nMsgStatus int8) string {
	msgStatus, ok := MessageStatusTipsMap[nMsgStatus]
	if ok {
		return msgStatus
	}

	return "UnKnow"
}

var MessageStatusTipsMap = map[int8]string{
	MessageStatus_Sending:   "发送中",
	MessageStatus_Sended:    "已发送",
	MessageStatus_Delivered: "已送达,未读",
	MessageStatus_Readed:    "已读",

	MessageStatus_HasDelete:   "您已删除好友",
	MessageStatus_NotFriend:   "对方不是您的好友",
	MessageStatus_BeenBlocked: "您已被对方拉黑",
	MessageStatus_BeenDeleted: "您已被对方删除好友",

	MessageStatus_GroupMuted:     "您已禁言",
	MessageStatus_NotInGroup:     "您不是该群成员",
	MessageStatus_GroupRemove:    "您已被移出该群组",
	MessageStatus_NotGroupMember: "您未加入该群组",
	MessageStatus_GroupExit:      "您已退出该群组",

	MessageStatus_SysError: "系统错误",
}

// 聊天消息状态
const (
	//	正常状态
	MessageStatus_Sending   = 1 //	发送中
	MessageStatus_Sended    = 2 //	已发送
	MessageStatus_Delivered = 3 //	已送达
	MessageStatus_Readed    = 4 //	已读

	//	单聊异常
	MessageStatus_HasDelete   = 11 //	您已删除好友
	MessageStatus_NotFriend   = 12 //	对方不是您的好友
	MessageStatus_BeenBlocked = 13 //	您已被对方拉黑
	MessageStatus_BeenDeleted = 14 //	您已被对方删除好友

	//	群聊异常
	MessageStatus_GroupMuted     = 16 //	您已禁言
	MessageStatus_GroupRemove    = 17 //	您已不是该群成员
	MessageStatus_NotInGroup     = 18 //	您不是该群成员
	MessageStatus_NotGroupMember = 19 //	您未加入该群组
	MessageStatus_GroupExit      = 20 //	您已退出该群组

	//	系统错误
	MessageStatus_SysError = 21 //	系统错误
)

// 聊天群角色
const (
	ChatGroupRole_Comm  = 0 //	普通用户
	ChatGroupRole_Admin = 1 //	管理岗
	ChatGroupRole_Owner = 2 //	群主
)
