package main

import (
	"ayj_chat_back/internal/model/chat"
	"ayj_chat_back/internal/service/chat"
	"fmt"
)

// 示例：使用新的消息推送策略

// 1、单聊消息示例
func ExamplePrivateMessages() {
	fmt.Println("=== 单聊消息推送策略示例 ===")

	// 场景1：普通聊天消息（双方都收到）
	normalChatMsg := &chat.MessageItem{
		MsgType:      "chat",
		MsgId:        "msg_001",
		SenderId:     "user_001",
		ReceiverId:   "user_002",
		ReceiverType: 1, // 单聊
		MsgContent:   "你好，最近怎么样？",
		PushStrategy: 1, // PushStrategyAll - 双方都收到
		PushTargets:  []string{},
		ExcludeUsers: []string{},
	}

	// 场景2：消息撤回通知（仅发送方收到）
	recallNoticeMsg := &chat.MessageItem{
		MsgType:      "notice",
		MsgId:        "msg_002", 
		SenderId:     "user_001",
		ReceiverId:   "user_002",
		ReceiverType: 1,
		MsgContent:   "您撤回了一条消息",
		PushStrategy: 2, // PushStrategySender - 仅发送方收到
		PushTargets:  []string{},
		ExcludeUsers: []string{},
	}

	// 场景3：好友申请消息（仅接收方收到）
	friendRequestMsg := &chat.MessageItem{
		MsgType:      "notice",
		MsgId:        "msg_003",
		SenderId:     "user_001",
		ReceiverId:   "user_002", 
		ReceiverType: 1,
		MsgContent:   "用户001申请添加您为好友",
		PushStrategy: 3, // PushStrategyReceiver - 仅接收方收到
		PushTargets:  []string{},
		ExcludeUsers: []string{},
	}

	fmt.Printf("普通聊天消息: %+v\n", normalChatMsg)
	fmt.Printf("撤回通知消息: %+v\n", recallNoticeMsg)
	fmt.Printf("好友申请消息: %+v\n", friendRequestMsg)
}

// 2、群聊消息示例
func ExampleGroupMessages() {
	fmt.Println("\n=== 群聊消息推送策略示例 ===")

	// 场景1：普通群聊消息（所有成员收到）
	normalGroupMsg := &chat.MessageItem{
		MsgType:      "chat",
		MsgId:        "msg_101",
		SenderId:     "user_001",
		ReceiverId:   "group_001",
		ReceiverType: 2, // 群聊
		MsgContent:   "大家好，今天天气不错",
		PushStrategy: 1, // PushStrategyAll - 所有成员收到
		PushTargets:  []string{},
		ExcludeUsers: []string{},
	}

	// 场景2：管理员通知（仅管理员收到）
	adminNoticeMsg := &chat.MessageItem{
		MsgType:      "notice",
		MsgId:        "msg_102",
		SenderId:     "system",
		ReceiverId:   "group_001",
		ReceiverType: 2,
		MsgContent:   "群管理员会议通知：明天下午2点开会",
		PushStrategy: 5, // PushStrategyGroupAdmin - 仅管理员收到
		PushTargets:  []string{},
		ExcludeUsers: []string{},
	}

	// 场景3：群主专属消息（仅群主收到）
	ownerOnlyMsg := &chat.MessageItem{
		MsgType:      "notice",
		MsgId:        "msg_103",
		SenderId:     "system",
		ReceiverId:   "group_001",
		ReceiverType: 2,
		MsgContent:   "群主权限：您的群即将到期，请及时续费",
		PushStrategy: 6, // PushStrategyGroupOwner - 仅群主收到
		PushTargets:  []string{},
		ExcludeUsers: []string{},
	}

	// 场景4：@指定用户消息
	mentionMsg := &chat.MessageItem{
		MsgType:      "chat",
		MsgId:        "msg_104",
		SenderId:     "user_001",
		ReceiverId:   "group_001",
		ReceiverType: 2,
		MsgContent:   "@张三 @李四 请注意查看重要通知",
		PushStrategy: 4, // PushStrategyCustom - 指定用户收到
		PushTargets:  []string{"user_002", "user_003"}, // 张三和李四的用户ID
		ExcludeUsers: []string{},
	}

	fmt.Printf("普通群聊消息: %+v\n", normalGroupMsg)
	fmt.Printf("管理员通知消息: %+v\n", adminNoticeMsg)
	fmt.Printf("群主专属消息: %+v\n", ownerOnlyMsg)
	fmt.Printf("@指定用户消息: %+v\n", mentionMsg)
}

// 3、便捷创建方法示例
func ExampleCreateMessageWithStrategy() {
	fmt.Println("\n=== 便捷创建方法示例 ===")

	// 创建仅发送给在线用户的群通知
	onlineNotice := chat.CreateMessageWithStrategy(
		"notice",                    // 消息类型
		"system",                    // 发送者
		"group_001",                 // 群ID
		2,                          // 群聊
		"系统维护通知：服务器将在30分钟后重启", // 内容
		7,                          // PushStrategyOnline - 仅在线用户
		nil,                        // 无指定目标
	)

	// 创建排除发送者的群消息
	excludeSenderMsg := chat.CreateMessageWithStrategy(
		"notice",           // 消息类型
		"user_001",         // 发送者
		"group_001",        // 群ID
		2,                  // 群聊
		"用户001退出了群聊", // 内容
		9,                  // PushStrategyNoSender - 排除发送者
		nil,                // 无指定目标
	)

	fmt.Printf("在线用户通知: %+v\n", onlineNotice)
	fmt.Printf("排除发送者消息: %+v\n", excludeSenderMsg)
}

// 4、实际业务场景示例
func ExampleBusinessScenarios() {
	fmt.Println("\n=== 实际业务场景示例 ===")

	// 业务场景1：好友添加成功后的消息推送
	sendFriendAddSuccessMessages := func(requesterId, targetId, requestMsg string) {
		// 给被申请方发送申请消息（如果有）
		if requestMsg != "" {
			applyMsg := &chat.MessageItem{
				MsgType:      "chat",
				SenderId:     requesterId,
				ReceiverId:   targetId,
				ReceiverType: 1,
				MsgContent:   requestMsg,
				PushStrategy: 3, // 仅接收方收到
			}
			fmt.Printf("申请消息: %+v\n", applyMsg)
		}

		// 给被申请方发送系统提示
		tipMsg := &chat.MessageItem{
			MsgType:      "notice",
			SenderId:     "system",
			ReceiverId:   targetId,
			ReceiverType: 1,
			MsgContent:   "您已添加了新好友，以上是打招呼的消息",
			PushStrategy: 3, // 仅接收方收到
		}

		// 给申请方发送通过消息
		passMsg := &chat.MessageItem{
			MsgType:      "notice",
			SenderId:     targetId,
			ReceiverId:   requesterId,
			ReceiverType: 1,
			MsgContent:   "我通过了你的朋友验证请求，现在我们可以开始聊天了",
			PushStrategy: 3, // 仅接收方收到
		}

		fmt.Printf("系统提示: %+v\n", tipMsg)
		fmt.Printf("通过消息: %+v\n", passMsg)
	}

	// 业务场景2：群成员变动通知
	sendGroupMemberChangeNotice := func(groupId, operatorId string, memberIds []string, action string) {
		var content string
		var strategy int8

		switch action {
		case "join":
			content = "新成员加入群聊"
			strategy = 9 // 排除发送者（新成员自己知道加入了）
		case "leave":
			content = "成员退出群聊"
			strategy = 9 // 排除发送者（退出的成员不需要收到）
		case "kick":
			content = "成员被移出群聊"
			strategy = 1 // 全部推送（包括被踢的成员）
		}

		noticeMsg := &chat.MessageItem{
			MsgType:      "notice",
			SenderId:     operatorId,
			ReceiverId:   groupId,
			ReceiverType: 2,
			MsgContent:   content,
			PushStrategy: strategy,
		}

		fmt.Printf("群成员变动通知: %+v\n", noticeMsg)
	}

	// 调用示例
	sendFriendAddSuccessMessages("user_001", "user_002", "你好，我想加你为好友")
	sendGroupMemberChangeNotice("group_001", "user_001", []string{"user_002"}, "join")
}

// 5、推送策略性能对比
func ExamplePerformanceComparison() {
	fmt.Println("\n=== 推送策略性能对比 ===")

	// 传统方式：所有消息都推送给所有相关用户
	traditionalWay := func() {
		fmt.Println("传统方式：")
		fmt.Println("- 群聊消息：推送给所有500个群成员")
		fmt.Println("- 管理员通知：推送给所有500个群成员（浪费）")
		fmt.Println("- 系统消息：推送给所有用户（浪费）")
		fmt.Println("- 推送量：每条消息 × 500用户 = 大量无效推送")
	}

	// 优化后方式：根据策略精准推送
	optimizedWay := func() {
		fmt.Println("\n优化后方式：")
		fmt.Println("- 群聊消息：推送给所有500个群成员")
		fmt.Println("- 管理员通知：仅推送给5个管理员")
		fmt.Println("- 系统消息：根据策略推送给目标用户")
		fmt.Println("- 推送量：精准推送，减少90%无效推送")
	}

	traditionalWay()
	optimizedWay()

	fmt.Println("\n性能提升：")
	fmt.Println("- 减少无效推送：90%")
	fmt.Println("- 降低服务器负载：显著")
	fmt.Println("- 提升用户体验：减少无关通知")
	fmt.Println("- 节省带宽：大幅减少")
}

func main() {
	ExamplePrivateMessages()
	ExampleGroupMessages()
	ExampleCreateMessageWithStrategy()
	ExampleBusinessScenarios()
	ExamplePerformanceComparison()
}
