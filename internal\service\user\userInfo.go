/*
******		FileName	:	userInfo.go
******		Describe	:	此文件主要用于用户个人信息的管理
******		Date		:	2024-11-12
******		Author		:	TangJinFei
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:   用户个人信息
 */

package server

import (
	"ayj_chat_back/api/user"
	"ayj_chat_back/internal/dao"
	"ayj_chat_back/internal/model/user"
	"ayj_chat_back/internal/public/tools"
	"context"
	"errors"
	"github.com/gogf/gf/v2/frame/g"
	"strings"
)

type ServerUserInfo struct{}

// 获取用户基本信息
func (s *ServerUserInfo) GetUserInfo(where interface{}) (res *user.UserInfoRes, err error) {
	result := dao.Db.Model(&modelUser.UserInfo{}).Where(where).Scan(&res)
	if res == nil {
		err = errors.New("用户不存在")
	}
	if result.Error != nil {
		err = result.Error
	}

	res.CreatedAt = tools.StrTimeToStringNMDHMS(res.CreatedAt)
	res.UpdatedAt = tools.StrTimeToStringNMDHMS(res.UpdatedAt)

	return
}

// 修改用户基本信息
func (s *ServerUserInfo) UpdateUserInfo(ctx context.Context, req *user.UserInfoUpdateReq) (res *user.UserInfoRes, err error) {
	// 获取当前用户ID
	userId := tools.GetUserIdFromCtx(ctx)
	//	只可以修改用户自己的基本信息
	if req.UserId != userId {
		return nil, errors.New("只能修改自己的基本信息")
	}

	// 1. 查询要修改的用户
	var objUserInfo modelUser.UserInfo
	result := dao.Db.Model(&modelUser.UserInfo{}).
		Where("user_id = ?", req.UserId).
		First(&objUserInfo)

	if result.Error != nil {
		return nil, result.Error
	}

	// 2. 使用请求map判断更新字段
	request := g.RequestFromCtx(ctx)
	requestMap := request.GetMap()

	// 3. 构建更新字段
	mapUpdateInfo := g.Map{}

	// 3.1 修改电子邮件
	if _, ok := requestMap["user_email"]; ok && req.UserEmail != "" {
		if tools.IsValidEmail(req.UserEmail) {
			mapUpdateInfo["user_email"] = req.UserEmail
		} else {
			return nil, errors.New("电子邮件格式错误")
		}
	}

	// 3.2 修改昵称
	if _, ok := requestMap["user_nick"]; ok && req.UserNick != "" {
		mapUpdateInfo["user_nick"] = req.UserNick
	}

	// 3.3 出生日期
	if _, ok := requestMap["user_birth"]; ok {
		mapUpdateInfo["user_birth"] = req.UserBirth
	}

	// 3.4 修改性别
	if _, ok := requestMap["user_sex"]; ok {
		if req.UserSex != 1 && req.UserSex != 2 && req.UserSex != 0 {
			return nil, errors.New("性别参数错误，应为0(未设置)、1(男)或2(女)")
		}
		mapUpdateInfo["user_sex"] = req.UserSex
	}

	// 3.5 修改区域
	if _, ok := requestMap["user_region"]; ok {
		mapUpdateInfo["user_region"] = req.UserRegion
	}

	// 3.6 修改头像
	if _, ok := requestMap["user_avatar"]; ok && req.UserAvatar != "" {
		mapUpdateInfo["user_avatar"] = req.UserAvatar
	}

	// 3.7 修改用户签名
	if _, ok := requestMap["user_signature"]; ok {
		mapUpdateInfo["user_signature"] = req.UserSignature
	}

	// 3.8 修改用户状态
	if _, ok := requestMap["user_status"]; ok {
		mapUpdateInfo["user_status"] = req.UserStatus
	}
	// 3.9 修改好友添加是否验证
	if _, ok := requestMap["friend_need_verify"]; ok {
		mapUpdateInfo["friend_need_verify"] = req.FriendNeedVerify
	}

	// 4. 如果没有更新字段，直接返回
	if len(mapUpdateInfo) == 0 {
		return nil, errors.New("未提供任何需要更新的字段")
	}

	// 5. 修改数据库信息
	result = dao.Db.Model(&modelUser.UserInfo{}).
		Where("user_id = ?", req.UserId).
		Updates(mapUpdateInfo)

	if result.Error != nil {
		return nil, result.Error
	}

	// 6. 返回更新后的用户信息
	res = &user.UserInfoRes{}
	result = dao.Db.Model(&modelUser.UserInfo{}).
		Where("user_id = ?", req.UserId).
		First(res)

	if result.Error != nil {
		return nil, result.Error
	}

	// 7. 格式化时间字段
	res.CreatedAt = tools.StrTimeToStringNMDHMS(res.CreatedAt)
	res.UpdatedAt = tools.StrTimeToStringNMDHMS(res.UpdatedAt)

	return res, nil
}

// 修改绑定手机
func (s *ServerUserInfo) ModifyUserPhone(req *user.UserPhoneModifyReq) (res *user.UserInfoRes, err error) {
	//	判断输入参数
	if !tools.IsValidPhoneNumber(req.UserPhoneNew) {
		err = errors.New("请输入正确的手机号")
		return
	}

	//	查询要修改的用户
	var objUserInfo *modelUser.UserInfo
	result := dao.Db.Model(&modelUser.UserInfo{}).Where(g.Map{"user_id": req.UserId}).Scan(&objUserInfo)
	if objUserInfo == nil {
		err = errors.New("用户不存在")
		return
	}

	if req.UserPhoneNew == objUserInfo.UserPhone {
		err = errors.New("新手机号与旧手机号一致!")
		return
	}

	if result.Error != nil {
		err = result.Error

	} else {
		result = dao.Db.Model(objUserInfo).Update("user_phone", req.UserPhoneNew)

		if result.Error != nil {
			err = result.Error
		} else {
			result = dao.Db.Model(&modelUser.UserInfo{}).Where(g.Map{"user_phone": req.UserPhoneNew}).Scan(&objUserInfo)
			res = &user.UserInfoRes{
				UserPhone:    objUserInfo.UserPhone,
				UserNick:     objUserInfo.UserNick,
				UserAvatar:   objUserInfo.UserAvatar,
				UserRoleType: objUserInfo.UserRoleType,
				UserSex:      objUserInfo.UserSex,
				UserEmail:    objUserInfo.UserEmail,
			}
		}
	}

	return
}

// 更加用户id 获取用户昵称
func (s *ServerUserInfo) GetUserNickFromId(stdUserId string) (strUserNick string) {
	if stdUserId == "" {
		strUserNick = ""
	} else {
		//	查询要修改的用户
		var objUserInfo *modelUser.UserInfo
		result := dao.Db.Model(&modelUser.UserInfo{}).Where(g.Map{"user_id": stdUserId}).Scan(&objUserInfo)
		if result.Error != nil || objUserInfo == nil {
			strUserNick = ""
			return
		}
		// 优先返回用户昵称，如果昵称为空则返回手机号
		if objUserInfo.UserNick != "" {
			strUserNick = objUserInfo.UserNick
		} else {
			strUserNick = objUserInfo.UserPhone
		}
	}

	return
}

// SearchUser 搜索用户
// 如果入参 search_info 包含'_', 表示user_id搜索，否则是手机号搜索
func (s *ServerUserInfo) SearchUser(req *user.SearchUserReq) (res *user.UserInfoRes, err error) {
	// 参数校验
	if req.SearchInfo == "" {
		return nil, errors.New("搜索信息不能为空")
	}
	// 判断搜索类型
	var where interface{}
	if strings.Contains(req.SearchInfo, "_") {
		// 使用索引加速查询
		where = g.Map{"user_id": req.SearchInfo}
	} else {
		// 手机号搜索，确保手机号格式正确
		if !tools.IsValidPhoneNumber(req.SearchInfo) {
			return nil, errors.New("请输入正确的手机号")
		}
		// 使用索引加速查询
		where = g.Map{"user_phone": req.SearchInfo}
	}

	result := dao.Db.Model(&modelUser.UserInfo{}).Where(where).Limit(1).Scan(&res)

	// 处理查询结果
	if result.Error != nil {
		return nil, result.Error
	}

	if res == nil {
		return nil, errors.New("用户不存在")
	}

	return
}

// 获取用户基本信息
func GetUserInfo(userId string) (userNick string, userAvatar string) {
	var userInfo struct {
		UserNick   string
		UserAvatar string
	}

	// 查询用户信息
	dao.Db.Table("user_info").Select("user_nick, user_avatar").Where("user_id = ?", userId).Scan(&userInfo)

	return userInfo.UserNick, userInfo.UserAvatar
}
