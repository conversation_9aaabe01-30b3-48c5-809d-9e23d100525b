package main

import (
	"ayj_chat_back/api/chat"
	"ayj_chat_back/internal/service/chat"
	"fmt"
)

// 示例：使用优化后的AddFriend接口
func ExampleAddFriend() {
	// 创建服务实例
	friendService := &server.ServerFriend{}

	// 示例1：添加不需要验证的用户
	fmt.Println("=== 示例1：添加不需要验证的用户 ===")
	req1 := &chat.AddFriendReq{
		UserId:        "user_002", // 目标用户ID
		RequestMsg:    "你好，我想加你为好友",
		RequestSource: 1, // 搜索添加
	}

	res1, err1 := friendService.AddFriend(req1, "user_001")
	if err1 != nil {
		fmt.Printf("错误: %v\n", err1)
	} else {
		fmt.Printf("状态: %d, 消息: %s\n", res1.AddStatus, res1.Message)
		// 预期输出: 状态: 3, 消息: 已成功添加为好友
	}

	// 示例2：添加需要验证的用户
	fmt.Println("\n=== 示例2：添加需要验证的用户 ===")
	req2 := &chat.AddFriendReq{
		UserId:        "user_003", // 目标用户ID（需要验证）
		RequestMsg:    "希望能成为朋友",
		RequestSource: 2, // 群聊添加
	}

	res2, err2 := friendService.AddFriend(req2, "user_001")
	if err2 != nil {
		fmt.Printf("错误: %v\n", err2)
	} else {
		fmt.Printf("状态: %d, 消息: %s\n", res2.AddStatus, res2.Message)
		// 预期输出: 状态: 1, 消息: 好友申请已发送
	}

	// 示例3：重复添加已有好友
	fmt.Println("\n=== 示例3：重复添加已有好友 ===")
	req3 := &chat.AddFriendReq{
		UserId:        "user_002", // 已经是好友的用户
		RequestMsg:    "再次添加",
		RequestSource: 1,
	}

	res3, err3 := friendService.AddFriend(req3, "user_001")
	if err3 != nil {
		fmt.Printf("错误: %v\n", err3)
	} else {
		fmt.Printf("状态: %d, 消息: %s\n", res3.AddStatus, res3.Message)
		// 预期输出: 状态: 2, 消息: 该用户已经是您的好友, 无需添加
	}

	// 示例4：添加不存在的用户
	fmt.Println("\n=== 示例4：添加不存在的用户 ===")
	req4 := &chat.AddFriendReq{
		UserId:        "user_999", // 不存在的用户
		RequestMsg:    "添加不存在的用户",
		RequestSource: 1,
	}

	res4, err4 := friendService.AddFriend(req4, "user_001")
	if err4 != nil {
		fmt.Printf("错误: %v\n", err4)
		// 预期输出: 错误: 用户不存在
	} else {
		fmt.Printf("状态: %d, 消息: %s\n", res4.AddStatus, res4.Message)
	}
}

// 示例：处理不同的返回状态
func HandleAddFriendResponse(res *chat.AddFriendRes, err error) {
	if err != nil {
		fmt.Printf("添加好友失败: %v\n", err)
		return
	}

	switch res.AddStatus {
	case 1:
		fmt.Println("✅ 好友申请已发送，等待对方处理")
		// 可以提示用户等待，或者跳转到申请列表页面

	case 2:
		fmt.Println("ℹ️ 该用户已经是您的好友")
		// 可以提示用户，或者直接跳转到聊天页面

	case 3:
		fmt.Println("🎉 成功添加为好友！")
		// 可以提示用户成功，或者直接跳转到聊天页面

	default:
		fmt.Printf("⚠️ 未知状态: %d\n", res.AddStatus)
	}

	fmt.Printf("详细信息: %s\n", res.Message)
}

// 示例：批量添加好友的处理逻辑
func BatchAddFriends(userIds []string, currentUserId string) {
	friendService := &server.ServerFriend{}

	fmt.Println("=== 批量添加好友 ===")

	for i, userId := range userIds {
		fmt.Printf("\n处理第 %d 个用户: %s\n", i+1, userId)

		req := &chat.AddFriendReq{
			UserId:        userId,
			RequestMsg:    "批量添加好友",
			RequestSource: 1,
		}

		res, err := friendService.AddFriend(req, currentUserId)
		HandleAddFriendResponse(res, err)
	}
}

func mainex() {
	// 运行示例
	ExampleAddFriend()

	// 批量添加示例
	userIds := []string{"user_004", "user_005", "user_006"}
	BatchAddFriends(userIds, "user_001")
}
