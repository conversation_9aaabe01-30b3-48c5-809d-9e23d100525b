# 消息推送策略设计优化方案

## 🎯 设计目标
基于微信的消息发送模式，设计灵活的消息推送策略，支持：
1. **单聊消息**：有的只需要发送给其中一方，有的需要双方都发送
2. **群聊消息**：有的需要发送给所有成员，有的只需要发送给管理员
3. **扩展性**：支持更多复杂的推送场景

## ✅ 优化内容

### 1. 数据表结构优化

#### 1.1 ChatMessage 表新增字段
```sql
-- 新增推送策略字段
ALTER TABLE chat_message ADD COLUMN push_strategy TINYINT DEFAULT 1 COMMENT '推送策略，1全部推送，2仅发送方，3仅接收方，4指定用户，5群管理员，6群所有者';
ALTER TABLE chat_message ADD COLUMN push_targets TEXT COMMENT '指定推送目标用户ID列表，JSON格式，当push_strategy=4时使用';

-- 添加索引
CREATE INDEX idx_push_strategy ON chat_message(push_strategy);
```

#### 1.2 推送策略常量定义
```go
const (
    // 基础推送策略
    PushStrategyAll      = 1  // 全部推送（默认）
    PushStrategySender   = 2  // 仅发送方
    PushStrategyReceiver = 3  // 仅接收方
    PushStrategyCustom   = 4  // 指定用户
    
    // 群聊专用推送策略
    PushStrategyGroupAdmin = 5  // 群管理员
    PushStrategyGroupOwner = 6  // 群所有者
    
    // 扩展推送策略
    PushStrategyOnline     = 7  // 仅在线用户
    PushStrategyOffline    = 8  // 仅离线用户
    PushStrategyNoSender   = 9  // 排除发送者
    PushStrategySystemOnly = 10 // 系统消息
)
```

### 2. MessageItem 结构体优化

#### 2.1 新增推送控制字段
```go
type MessageItem struct {
    // 原有字段...
    MsgType      string
    MsgId        string
    SenderId     string
    ReceiverId   string
    ReceiverType int8
    MsgContent   string
    // ...

    // 新增：推送策略控制
    PushStrategy int8     // 推送策略
    PushTargets  []string // 指定推送目标用户ID列表
    ExcludeUsers []string // 排除推送的用户ID列表
    Extra        map[string]interface{} // 扩展字段
}
```

### 3. 核心推送逻辑

#### 3.1 策略解析函数
```go
func (s *ServerChat) getTargetUsersByStrategy(msg *MessageItem) ([]string, error) {
    switch msg.PushStrategy {
    case PushStrategyAll:      // 全部推送
    case PushStrategySender:   // 仅发送方
    case PushStrategyReceiver: // 仅接收方
    case PushStrategyCustom:   // 指定用户
    case PushStrategyGroupAdmin: // 群管理员
    case PushStrategyGroupOwner: // 群所有者
    // ...
    }
}
```

#### 3.2 分离推送方法
```go
// 单聊消息推送
func (s *ServerChat) deliverPrivateMessageWithStrategy(msg *MessageItem, targetUsers []string)

// 群聊消息推送  
func (s *ServerChat) deliverGroupMessageWithStrategy(msg *MessageItem, targetUsers []string)
```

## 🔧 使用场景示例

### 1. 单聊消息场景

#### 场景1：普通聊天消息（双方都收到）
```go
msg := &MessageItem{
    MsgType:      "chat",
    SenderId:     "user_001",
    ReceiverId:   "user_002", 
    ReceiverType: 1,
    MsgContent:   "你好",
    PushStrategy: PushStrategyAll, // 双方都收到
}
```

#### 场景2：消息撤回通知（仅发送方收到）
```go
msg := &MessageItem{
    MsgType:      "notice",
    SenderId:     "user_001",
    ReceiverId:   "user_002",
    ReceiverType: 1,
    MsgContent:   "消息已撤回",
    PushStrategy: PushStrategySender, // 仅发送方收到
}
```

#### 场景3：好友申请消息（仅接收方收到）
```go
msg := &MessageItem{
    MsgType:      "notice",
    SenderId:     "user_001", 
    ReceiverId:   "user_002",
    ReceiverType: 1,
    MsgContent:   "好友申请",
    PushStrategy: PushStrategyReceiver, // 仅接收方收到
}
```

### 2. 群聊消息场景

#### 场景1：普通群聊消息（所有成员收到）
```go
msg := &MessageItem{
    MsgType:      "chat",
    SenderId:     "user_001",
    ReceiverId:   "group_001",
    ReceiverType: 2,
    MsgContent:   "大家好",
    PushStrategy: PushStrategyAll, // 所有成员收到
}
```

#### 场景2：管理员通知（仅管理员收到）
```go
msg := &MessageItem{
    MsgType:      "notice",
    SenderId:     "system",
    ReceiverId:   "group_001", 
    ReceiverType: 2,
    MsgContent:   "群管理通知",
    PushStrategy: PushStrategyGroupAdmin, // 仅管理员收到
}
```

#### 场景3：群主专属消息（仅群主收到）
```go
msg := &MessageItem{
    MsgType:      "notice",
    SenderId:     "system",
    ReceiverId:   "group_001",
    ReceiverType: 2, 
    MsgContent:   "群主权限通知",
    PushStrategy: PushStrategyGroupOwner, // 仅群主收到
}
```

#### 场景4：指定用户消息
```go
msg := &MessageItem{
    MsgType:      "notice",
    SenderId:     "user_001",
    ReceiverId:   "group_001",
    ReceiverType: 2,
    MsgContent:   "@特定用户的消息", 
    PushStrategy: PushStrategyCustom,
    PushTargets:  []string{"user_002", "user_003"}, // 指定用户收到
}
```

### 3. 便捷创建方法

```go
// 创建带推送策略的消息
msg := CreateMessageWithStrategy(
    "chat",           // 消息类型
    "user_001",       // 发送者
    "group_001",      // 接收者
    2,                // 群聊
    "管理员消息",      // 内容
    PushStrategyGroupAdmin, // 仅管理员
    nil,              // 无指定目标
)

// 发送消息
globalWsData.messageQueue <- msg
```

## 📊 推送策略对照表

| 策略值 | 策略名称 | 单聊行为 | 群聊行为 | 使用场景 |
|--------|----------|----------|----------|----------|
| 1 | 全部推送 | 发送方+接收方 | 所有成员 | 普通聊天消息 |
| 2 | 仅发送方 | 仅发送方 | 仅发送方 | 消息撤回确认 |
| 3 | 仅接收方 | 仅接收方 | 所有成员(排除发送方) | 好友申请、入群通知 |
| 4 | 指定用户 | PushTargets指定 | PushTargets指定 | @消息、定向通知 |
| 5 | 群管理员 | 无效 | 管理员+群主 | 管理通知 |
| 6 | 群所有者 | 无效 | 仅群主 | 群主权限通知 |
| 7 | 仅在线用户 | 在线的发送方+接收方 | 在线的所有成员 | 实时通知 |
| 9 | 排除发送者 | 仅接收方 | 所有成员(排除发送方) | 系统通知 |

## 🚀 性能优化

### 1. 数据库优化
- 添加 `push_strategy` 索引，提高策略查询效率
- `push_targets` 使用 JSON 格式存储，支持复杂目标列表
- 合理使用分表策略，避免单表过大

### 2. 内存优化
- 缓存群成员列表，减少数据库查询
- 在线用户状态缓存，快速过滤目标用户
- 推送目标用户去重，避免重复推送

### 3. 推送优化
- 异步推送，不阻塞主流程
- 批量推送，提高推送效率
- 失败重试机制，保证消息可达性

## 📋 扩展性设计

### 1. 自定义推送策略
```go
// 支持自定义推送逻辑
type CustomPushStrategy func(msg *MessageItem) ([]string, error)

// 注册自定义策略
func RegisterCustomStrategy(strategyId int8, handler CustomPushStrategy)
```

### 2. 推送中间件
```go
// 推送前置处理
type PushMiddleware func(msg *MessageItem, targetUsers []string) ([]string, error)

// 支持推送中间件链
func AddPushMiddleware(middleware PushMiddleware)
```

### 3. 推送统计
```go
// 推送统计信息
type PushStats struct {
    TotalMessages int64
    SuccessCount  int64
    FailureCount  int64
    OnlineUsers   int64
    OfflineUsers  int64
}
```

## ✅ 优势总结

1. **灵活性**：支持多种推送策略，满足不同业务场景
2. **性能**：优化推送逻辑，减少不必要的消息推送
3. **扩展性**：易于添加新的推送策略和自定义逻辑
4. **兼容性**：向后兼容现有消息推送机制
5. **可维护性**：清晰的代码结构，便于维护和调试

这个设计方案完全满足您提出的需求，支持单聊和群聊的灵活消息推送策略，同时具有良好的扩展性和性能。
