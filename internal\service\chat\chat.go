/*
******		FileName	:	chat.go
******		Describe	:	此文件主要用于聊天服务实现
******		Date		:	2024-04-03
******		Author		:	TangJinFei
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:   聊天服务相关的业务逻辑实现
 */

package server

import (
	"ayj_chat_back/api/chat"
	"ayj_chat_back/internal/consts"
	"ayj_chat_back/internal/dao"
	"ayj_chat_back/internal/model/chat"
	"ayj_chat_back/internal/public/middleware"
	"ayj_chat_back/internal/public/response"
	"ayj_chat_back/internal/public/tools"
	"ayj_chat_back/internal/public/uniqueId"
	"context"
	"encoding/json"
	"errors"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gorilla/websocket"
	"gorm.io/gorm"
	"sync"
	"time"
)

// ServerChat 聊天服务结构体
type ServerChat struct {
	UserConnections map[string]map[int]*UserConnection // UserId -> loingType -> 连接	第一层是用户id， 第二层 是登录方式，第三层才是连接信息
	connectionsMux  sync.RWMutex                       // 连接管理锁
	// 消息队列相关
	messageQueue chan *MessageItem // 消息队列

}

// UserConnection 用户连接信息
type UserConnection struct {
	UserId     string          // 用户ID
	UserNick   string          // 用户昵称
	UserAvatar string          // 用户头像
	WsConn     *websocket.Conn // WebSocket连接
	LastPing   time.Time       // 最后一次心跳时间

	LoginType int // 登录方式 ， 1 手机app 登录， 2 web 登录， 3 pc app 登录
	LoginRole int // 用户角色

	Ctx       context.Context    // 上下文
	CancelCtx context.CancelFunc // 取消函数
	WriteMux  sync.Mutex         // 写入锁，保护WebSocket连接的并发写入
}

// MessageItem 消息队列项 - 优化版本，支持灵活的推送策略
type MessageItem struct {
	// 1、消息基本信息
	MsgType     string // 消息类型：chat, system, notice
	MsgId       string // 消息统一ID
	MsgClientId string // 客户端发送消息的ID, 发送端用来判断,其他端收到的为空

	// 2、接收者信息
	ReceiverId   string // 接收者ID（单聊为用户ID，群聊为群ID）
	ReceiverType int8   // 接收者类型：1单聊，2群聊
	ConvId       string // 会话ID

	// 3、消息内容
	MsgContentFmt  int8   // 内容格式：1 JSON，2 Protobuf
	MsgContentType string // 内容类型：text，image，audio，video, file等
	MsgContent     string // 消息内容

	// 4、发送者信息
	SenderId     string      // 发送者ID
	SenderNick   string      // 发送者昵称
	SenderAvatar string      // 发送者头像
	SendTime     *gtime.Time // 消息发送时间

	// 5、新增：推送策略控制
	PushStrategy int8     // 推送策略：1全部推送，2仅发送方，3仅接收方，4指定用户，5群管理员，6群所有者等
	PushTargets  []string // 指定推送目标用户ID列表（当PushStrategy=4时使用）
	ExcludeUsers []string // 排除推送的用户ID列表（可选）

	// 6、扩展字段
	Extra map[string]interface{} // 额外信息，用于扩展
}

// WebSocketMessage WebSocket消息结构
type WebSocketMessage struct {
	MsgType     string `v:"required#请输入消息类型(msg_type)" json:"msg_type"`           // ws 消息类型: chat 聊天消息, chat_notice 聊天通知, system 系统信息, ping 心跳信息
	MsgClientId string `v:"required#请输入消息id(msg_client_id)" json:"msg_client_id"` // ws 客户端发送的消息id,客户端自己用于判断

	MsgReceiverId   string `v:"required#请消息接收id(msg_receiver_id)" json:"msg_receiver_id"`                       // ws 消息接收id， 单聊：对方user_id, 群聊 ：group_id
	MsgReceiverType int8   `v:"required|in:1,2#请消息接收类型(1 单聊;2 群聊)|请消息接收类型(1 单聊;2 群聊)" json:"msg_receiver_type"` // ws 消息接收类型 1 单聊， 2 群聊

	MsgContentFmt  int8   `v:"required|in:1,2#请输入消内容息格式(msg_content_fmt)|消息内容格式(1,2)" json:"msg_content_fmt"` // ws 消息内容格式 1 json, 2 protobuf
	MsgContentType string `v:"required#请输入消息内容类型(msg_type)" json:"msg_content_type"`                          // ws 消息内容类型  text，image，audio，video ,file
	MsgContent     string `v:"required#消息内容为空(msg_content)" json:"msg_content"`                               // ws 消息内容 可扩展
}

// 初始化聊天服务
var (
	globalWsData *ServerChat
	once         sync.Once
	messageDb    *ServerMessage
)

// 初始化全局数据
func init() {
	globalWsData = getChatServer()
}

// 获取聊天服务单例
func getChatServer() *ServerChat {
	once.Do(func() {
		globalWsData = &ServerChat{
			UserConnections: make(map[string]map[int]*UserConnection),
			messageQueue:    make(chan *MessageItem, 10000), // 可根据实际需求调整缓冲区大小

		}

		// 初始化消息数据库服务
		messageDb = &ServerMessage{
			shardCount: 10, // 设置分表数量，可以根据实际需求调整
		}

		//groupInfo = &ServerGroup{}
		// 启动消息处理协程
		go globalWsData.processMessageQueue()

		// 启动心跳检测
		//go globalWsData.heartbeatChecker()
	})

	return globalWsData
}

// 处理WebSocket连接
func (s *ServerChat) HandelWsLogin(r *ghttp.Request, ws *websocket.Conn) (err error, nRetCode int) {
	//	解析token
	claims, err, nRetCode := middleware.ParseToken(r)
	if 0 != nRetCode {
		g.Log().Debugf(r.Context(), "WebSocket登录失败 : %v, 错误码: %d", err, nRetCode)
		return err, nRetCode
	}
	// 创建连接上下文
	ctx, cancelFunc := context.WithCancel(context.Background())
	// 创建用户连接
	conn := &UserConnection{
		UserId:     claims.UserId,
		UserNick:   claims.UserNick,
		UserAvatar: claims.UserAvatar,
		WsConn:     ws,
		LastPing:   time.Now(),
		LoginType:  claims.LoginType,
		LoginRole:  claims.UserRoleType,
		Ctx:        ctx,
		CancelCtx:  cancelFunc,
	}
	// 注册连接
	s.registerConnection(claims.UserId, claims.LoginType, conn)
	// 处理连接
	go s.handleConnection(claims.UserId, claims.LoginType, conn)

	g.Log().Infof(context.Background(), "ws 用户 登录成功: %s_%s_%s  ", claims.UserId, conn.UserNick, consts.LoginTypeToString(claims.LoginType))

	return nil, 0
}

// 注册用户连接
func (s *ServerChat) registerConnection(strUserId string, nLoginType int, conn *UserConnection) {
	globalWsData.connectionsMux.Lock()
	defer globalWsData.connectionsMux.Unlock()

	//	当前用户是否登录过
	if oldConnUser, existsUser := globalWsData.UserConnections[strUserId]; existsUser {
		// 如果同一种登录方式已存在连接，先关闭旧连接
		if oldConn, exists := oldConnUser[nLoginType]; exists {
			//	通知掉线
			oldConn.CancelCtx()
			//	关闭连接
			oldConn.WsConn.Close()
			//	删除之前的连接
			delete(globalWsData.UserConnections[strUserId], nLoginType)
			//	写入日志
			g.Log().Infof(context.Background(), "ws 用户 重复连接，关闭旧连接: %s_%s_%s", strUserId, conn.UserNick, consts.LoginTypeToString(nLoginType))
		}
	} else {
		globalWsData.UserConnections[strUserId] = make(map[int]*UserConnection) //conn
	}
	// 初始化连接的写入锁
	conn.WriteMux = sync.Mutex{}
	// 注册新连接
	globalWsData.UserConnections[strUserId][nLoginType] = conn
}

// 注销用户连接
func (s *ServerChat) unregisterConnection(strUserId string, nLoginType int) {
	globalWsData.connectionsMux.Lock()
	defer globalWsData.connectionsMux.Unlock()
	// 删除连接
	if oldConnUser, existsUser := globalWsData.UserConnections[strUserId]; existsUser {
		// 如果同一种登录方式已存在连接，先关闭旧连接
		if oldConn, exists := oldConnUser[nLoginType]; exists {
			//	通知掉线
			oldConn.CancelCtx()
			//	关闭连接
			oldConn.WsConn.Close()
			//	删除之前的连接
			delete(globalWsData.UserConnections[strUserId], nLoginType)
			//	如果当前用户所有的连接方式都掉线了
			if len(globalWsData.UserConnections[strUserId]) == 0 {
				delete(globalWsData.UserConnections, strUserId)
			}
			// 记录日志
			g.Log().Infof(context.Background(), "ws 用户 已断开连接: %s_%s_%s ", strUserId, oldConn.UserNick, consts.LoginTypeToString(nLoginType))
		}
	}
}

// 处理用户连接
func (s *ServerChat) handleConnection(strUserId string, nLoginType int, conn *UserConnection) {
	defer func() {
		// 连接结束时注销
		s.unregisterConnection(strUserId, nLoginType)
	}()

	// 设置读取超时
	//conn.Conn.SetReadDeadline(time.Now().Add(time.Minute * 5))

	// 设置Pong处理函数
	conn.WsConn.SetPongHandler(func(string) error {
		conn.LastPing = time.Now()
		//conn.Conn.SetReadDeadline(time.Now().Add(time.Minute * 5))
		return nil
	})

	// 读取消息循环
	for {
		// 检查上下文是否已取消
		select {
		case <-conn.Ctx.Done():
			return
		default:
			// 继续执行
		}
		// 读取消息
		messageType, message, err := conn.WsConn.ReadMessage()

		if err != nil {
			g.Log().Info(context.Background(), "读取WebSocket消息错误: %v", err)
			return // 连接出错时正确关闭
		}
		// 更新最后心跳时间
		conn.LastPing = time.Now()
		// 处理消息
		if messageType == websocket.TextMessage {
			s.handleWebSocketMessage(conn, message)
		}
	}
}

// 处理WebSocket消息
func (s *ServerChat) handleWebSocketMessage(conn *UserConnection, message []byte) {

	ctx := context.Background()
	g.Log().Debugf(ctx, "收到用户的消息:%s", string(message))
	// 解析消息
	var wsMessage WebSocketMessage
	err := json.Unmarshal(message, &wsMessage)
	if err != nil {
		response.WsError(conn.WsConn, "数据格式错误", 2, message)
		return
	}
	// 使用GoFrame验证器
	if err := g.Validator().Data(wsMessage).Run(context.Background()); err != nil {
		response.WsError(conn.WsConn, err.Error(), 400, nil)
		return
	}
	// 根据消息类型处理
	switch wsMessage.MsgType {
	case "chat":
		s.handleSendMessage(conn, &wsMessage) // 处理发送消息
	case "receipt":
		// 处理已读回执
		s.handleReadReceipt(conn, wsMessage.MsgContent)
	case "ping":
		// 处理心跳
		conn.WsConn.WriteMessage(websocket.PongMessage, []byte{})
	default:
		g.Log().Warningf(context.Background(), "未知的WebSocket消息类型: %s", wsMessage.MsgType)
	}
}

// 验证单聊好友关系
func (s *ServerChat) verifyFriendRelation(userId string, friendId string) (bOk bool, nStatusRet int8) {
	// 2. 检查对方是否拉黑了自己
	var theirBlackInfo modelChat.FriendBlackList
	result := dao.Db.Model(&modelChat.FriendBlackList{}).Where(
		"user_id = ? AND blocked_id = ? AND deleted_at IS NULL",
		friendId, userId,
	).First(&theirBlackInfo)

	if result.Error == nil && result.RowsAffected > 0 {
		return false, consts.MessageStatus_BeenBlocked // 您已被对方拉黑

	}
	// 3. 检查双方的好友关系
	// 3.1 检查自己到对方的好友关系
	var myFriendRelation modelChat.FriendRelation
	result = dao.Db.Unscoped().Model(&modelChat.FriendRelation{}).
		Where("user_id = ? AND friend_id = ?", userId, friendId).
		First(&myFriendRelation)

	myFriendExists := result.Error == nil && result.RowsAffected > 0       // 自己存在记录
	myFriendDeleted := myFriendExists && myFriendRelation.DeletedAt != nil // 自己删除

	// 3.2 检查对方到自己的好友关系
	var theirFriendRelation modelChat.FriendRelation
	result = dao.Db.Unscoped().Model(&modelChat.FriendRelation{}).
		Where("user_id = ? AND friend_id = ?", friendId, userId).
		First(&theirFriendRelation)

	theirFriendExists := result.Error == nil && result.RowsAffected > 0             // 对方存在记录
	theirFriendDeleted := theirFriendExists && theirFriendRelation.DeletedAt != nil // 对方删除了

	// 4. 判断好友关系状态

	// 4.1 双方都删除了
	if myFriendDeleted && theirFriendDeleted {
		return false, consts.MessageStatus_NotFriend
	}
	// 4.2 双方都不存在记录，不是好友
	if !myFriendExists && !theirFriendExists {
		return false, consts.MessageStatus_NotFriend
	}
	// 4.3 自己删除了对方
	if myFriendExists && myFriendDeleted {
		return false, consts.MessageStatus_HasDelete
	}
	// 4.4 对方删除了自己
	if myFriendExists && theirFriendDeleted {
		return false, consts.MessageStatus_BeenDeleted
	}

	// 5. 双方都是有效的好友关系
	return true, consts.MessageStatus_Sending
}

// 验证群成员关系
func (s *ServerChat) verifyGroupMember(userId string, groupId string) (bOk bool, nStatusRet int8) {
	// 1. 查询群成员表（包含软删除的记录）
	var groupMember modelChat.ChatGroupMember
	result := dao.Db.Unscoped().Model(&modelChat.ChatGroupMember{}).
		Where("group_id = ? AND user_id = ?", groupId, userId).
		First(&groupMember)

	// 2. 处理查询结果
	if result.Error != nil {
		if result.RowsAffected == 0 {
			// 从未加入过群
			return false, consts.MessageStatus_NotGroupMember
		}
		// 查询出错
		return false, consts.MessageStatus_SysError

	}

	// 3. 判断成员状态
	now := gtime.Now()

	// 3.1 检查是否主动退群
	if groupMember.ExitTime != nil {
		return false, consts.MessageStatus_GroupExit
	}
	// 3.2 检查是否被移除（软删除）
	if groupMember.DeletedAt != nil {
		return false, consts.MessageStatus_GroupRemove
	}

	// 3.3 检查禁言状态
	isMuted := false
	if groupMember.MuteEndTime != nil && groupMember.MuteEndTime.After(now) {
		isMuted = true
	}

	// 4.1 如果被禁言，更新状态信息
	if isMuted {
		return false, consts.MessageStatus_GroupMuted

	} else {
		return true, consts.MessageStatus_Sending
	}
}

// 消息项转 基本信息
func (s *ServerChat) msgItemToMsgBasicInfo(msg *MessageItem, msgStatus int8, bIsMyself bool) (objInfoRet *chat.MsgBasicInfo) {
	// 1. 聊天信息
	chatInfo := &chat.ChatMsgInfo{
		MsgClientId: msg.MsgClientId,
		MsgId:       msg.MsgId,

		IsMyself: bIsMyself,
		MsgInfo:  msg.MsgContent,

		Status:        msgStatus, // 已发送	消息状态，1 发送中，2已发送，3已送达，4已读
		StatusTips:    consts.GetMsgStatusTips(msgStatus),
		ReadTimestamp: 0,
	}

	msgInfoRet := &chat.MsgBasicInfo{
		//	消息基本信息
		MsgType: msg.MsgType,

		//	消息发送者信息
		SenderId:      msg.SenderId,
		SendTimestamp: tools.GtimeToTimestamp(msg.SendTime),
		SenderNick:    msg.SenderNick,
		SenderAvatar:  msg.SenderAvatar,

		//	信息内容
		MsgContentFmt:  msg.MsgContentFmt,
		MsgContentType: msg.MsgContentType,
		MsgContent:     chatInfo,

		//	接受者信息
		ConvId:   msg.ConvId,
		ConvType: msg.ReceiverType,
	}

	return msgInfoRet
}

// 处理发送消息
func (s *ServerChat) handleSendMessage(conn *UserConnection, wsMessage *WebSocketMessage) {
	// 1. 验证接收者类型
	if wsMessage.MsgReceiverType != 1 && wsMessage.MsgReceiverType != 2 {
		response.WsError(conn.WsConn, "接收者类型无效", 500, nil)
		return
	}

	// 2. 生成消息ID
	msgId := uniqueId.GenerateMessageID()
	// 3. 创建消息体， 用以分发
	timeNow := gtime.Now()
	msg := &MessageItem{
		MsgType:      wsMessage.MsgType,
		MsgId:        msgId,
		MsgClientId:  wsMessage.MsgClientId,
		ConvId:       messageDb.generateConvId(wsMessage.MsgReceiverType, conn.UserId, wsMessage.MsgReceiverId),
		ReceiverId:   wsMessage.MsgReceiverId,
		ReceiverType: wsMessage.MsgReceiverType,

		MsgContentFmt:  wsMessage.MsgContentFmt,
		MsgContentType: wsMessage.MsgContentType,
		MsgContent:     wsMessage.MsgContent,

		SenderId:     conn.UserId,
		SenderNick:   conn.UserNick,
		SenderAvatar: conn.UserAvatar,
		SendTime:     timeNow,
	}

	// 4. 构建消息内容,用以返回数据
	chatInfo := &chat.ChatMsgInfo{
		MsgClientId: msg.MsgClientId,
		MsgId:       msg.MsgId,

		IsMyself: true,
		MsgInfo:  wsMessage.MsgContent,

		Status:        consts.MessageStatus_Sended, // 已发送	消息状态，1 发送中，2已发送，3已送达，4已读
		StatusTips:    consts.GetMsgStatusTips(consts.MessageStatus_Sended),
		ReadTimestamp: 0,
	}

	//	获取ws 发送的消息
	msgSendInfo := s.msgItemToMsgBasicInfo(msg, consts.MessageStatus_Sended, true)

	//	2. 判断接收者 是否有效
	if wsMessage.MsgReceiverType == consts.ChatMsgType_Private {
		// 1. 单聊验证，验证双方好友关系和黑名单状态
		isFriend, nStatus := s.verifyFriendRelation(conn.UserId, wsMessage.MsgReceiverId)
		if isFriend != true {
			chatInfo.Status = nStatus
			chatInfo.StatusTips = consts.GetMsgStatusTips(chatInfo.Status)
			msgSendInfo.MsgContent = chatInfo
			response.WsSuccess(conn.WsConn, chatInfo.StatusTips, msgSendInfo)
			return
		}

	} else if wsMessage.MsgReceiverType == consts.ChatMsgType_Group {
		// 2. 群聊，验证自己是否是群成员（包含禁言状态检查）
		isInGroup, nStatus := s.verifyGroupMember(conn.UserId, wsMessage.MsgReceiverId)
		if isInGroup != true {
			chatInfo.Status = nStatus
			chatInfo.StatusTips = consts.GetMsgStatusTips(chatInfo.Status)
			msgSendInfo.MsgContent = chatInfo
			response.WsSuccess(conn.WsConn, chatInfo.StatusTips, msgSendInfo)
			return
		}
	}

	// 5. 将消息放入队列 尝试发送，不关心返回值
	var err error
	select {
	case globalWsData.messageQueue <- msg:
		err = nil
	default:
		err = errors.New("缓冲区已满，无法发送")
	}

	globalWsData.connectionsMux.RLock()
	connCurUser, online := globalWsData.UserConnections[msg.SenderId]
	globalWsData.connectionsMux.RUnlock()

	//	将消息返回给消息发送者
	if online {
		for _, connSub := range connCurUser {
			// 使用匿名函数确保锁的正确释放
			func(connection *UserConnection) {
				// 加锁保护WebSocket连接的写操作
				connection.WriteMux.Lock()
				defer connection.WriteMux.Unlock()

				// 如果找不到连接信息，直接发送（这种情况不应该发生，但为了健壮性添加）
				if err != nil {
					response.WsError(connSub.WsConn, err.Error(), 2, msgSendInfo)
				} else {
					response.WsSuccess(connSub.WsConn, "发送成功", msgSendInfo)
				}
			}(connSub)
		}
	}
}

// 投递消息 - 优化版本，支持推送策略
func (s *ServerChat) deliverMessage(msg *MessageItem) {
	// 1、根据推送策略获取目标用户列表
	targetUsers, err := s.getTargetUsersByStrategy(msg)
	if err != nil {
		g.Log().Errorf(context.Background(), "获取推送目标用户失败: %v", err)
		return
	}

	// 2、如果没有目标用户，直接返回
	if len(targetUsers) == 0 {
		g.Log().Debugf(context.Background(), "消息 %s 没有推送目标用户", msg.MsgId)
		return
	}

	// 3、根据接收者类型处理消息推送
	if msg.ReceiverType == 1 {
		// 单聊消息
		s.deliverPrivateMessageWithStrategy(msg, targetUsers)
	} else {
		// 群聊消息
		s.deliverGroupMessageWithStrategy(msg, targetUsers)
	}
}

// 推送服务器消息
func (s *ServerChat) PushServerMsgInfo(wsMessage *WebSocketMessage, sendUserId, SenderNick, SenderAvatar string) {
	msgId := uniqueId.GenerateMessageID()
	// 3. 创建消息体， 用以分发
	timeNow := gtime.Now()
	msg := &MessageItem{
		MsgType:      wsMessage.MsgType,
		MsgId:        msgId,
		MsgClientId:  wsMessage.MsgClientId,
		ConvId:       messageDb.generateConvId(wsMessage.MsgReceiverType, sendUserId, wsMessage.MsgReceiverId),
		ReceiverId:   wsMessage.MsgReceiverId,
		ReceiverType: wsMessage.MsgReceiverType,

		MsgContentFmt:  wsMessage.MsgContentFmt,
		MsgContentType: wsMessage.MsgContentType,
		MsgContent:     wsMessage.MsgContent,

		SenderId:     sendUserId,
		SenderNick:   SenderNick,
		SenderAvatar: SenderAvatar,
		SendTime:     timeNow,
	}

	// 4. 构建消息内容,用以返回数据
	chatInfo := &chat.ChatMsgInfo{
		MsgClientId: msg.MsgClientId,
		MsgId:       msg.MsgId,

		IsMyself: true,
		MsgInfo:  wsMessage.MsgContent,

		Status:        consts.MessageStatus_Sended, // 已发送	消息状态，1 发送中，2已发送，3已送达，4已读
		StatusTips:    consts.GetMsgStatusTips(consts.MessageStatus_Sended),
		ReadTimestamp: 0,
	}

	//	获取ws 发送的消息
	msgSendInfo := s.msgItemToMsgBasicInfo(msg, consts.MessageStatus_Sended, true)

	//	2. 判断接收者 是否有效
	if wsMessage.MsgReceiverType == consts.ChatMsgType_Private {
		// 1. 单聊验证，验证双方好友关系和黑名单状态
		isFriend, nStatus := s.verifyFriendRelation(conn.UserId, wsMessage.MsgReceiverId)
		if isFriend != true {
			chatInfo.Status = nStatus
			chatInfo.StatusTips = consts.GetMsgStatusTips(chatInfo.Status)
			msgSendInfo.MsgContent = chatInfo
			response.WsSuccess(conn.WsConn, chatInfo.StatusTips, msgSendInfo)
			return
		}

	} else if wsMessage.MsgReceiverType == consts.ChatMsgType_Group {
		// 2. 群聊，验证自己是否是群成员（包含禁言状态检查）
		isInGroup, nStatus := s.verifyGroupMember(conn.UserId, wsMessage.MsgReceiverId)
		if isInGroup != true {
			chatInfo.Status = nStatus
			chatInfo.StatusTips = consts.GetMsgStatusTips(chatInfo.Status)
			msgSendInfo.MsgContent = chatInfo
			response.WsSuccess(conn.WsConn, chatInfo.StatusTips, msgSendInfo)
			return
		}
	}

	// 5. 将消息放入队列 尝试发送，不关心返回值
	var err error
	select {
	case globalWsData.messageQueue <- msg:
		err = nil
	default:
		err = errors.New("缓冲区已满，无法发送")
	}
}

// 处理单聊消息
func (s *ServerChat) deliverPrivateMessage(msg *MessageItem) {
	// 检查接收者是否在线
	globalWsData.connectionsMux.RLock()
	connUser, online := globalWsData.UserConnections[msg.ReceiverId]
	globalWsData.connectionsMux.RUnlock()

	//	获取ws 发送的消息
	msgSendInfo := s.msgItemToMsgBasicInfo(msg, consts.MessageStatus_Sended, false)

	// 将消息体 保存到数据库
	messageDb.savePrivateMessageToDatabase(msg)

	// 如果在线，直接发送
	if online {
		//	同一个用户， 几个登录端都需要发送
		for _, conn := range connUser {
			var bIsSendOk = false
			func(_connection *UserConnection, _bIsSendOk *bool) {
				// 加锁保护WebSocket连接的写操作, 使用defer确保锁一定会被释放
				_connection.WriteMux.Lock()
				defer _connection.WriteMux.Unlock()

				err := response.WsSuccess(_connection.WsConn, "发送成功", msgSendInfo)
				if err != nil {
					g.Log().Errorf(context.Background(), "发送WebSocket消息错误: %v", err)
				} else {
					bIsSendOk = true
				}

			}(conn, &bIsSendOk)
			//	只要有一个端发送成功， 就更新数据库发送成功
			if bIsSendOk {
				// 更新消息状态为已送达
				messageDb.updateMessageStatus(msg.MsgId, msg.ReceiverId, consts.MessageStatus_Delivered)
			}
		}
	}
}

// 投递群聊消息
func (s *ServerChat) deliverGroupMessage(msg *MessageItem) {
	//	获取ws 发送的消息
	msgSendInfo := s.msgItemToMsgBasicInfo(msg, consts.MessageStatus_Sended, false)
	/*chatInfo := &chat.ChatMsgInfo{
		MsgClientId: "",
		MsgId:       msg.MsgId,

		IsMyself: true,
		MsgInfo:  "",

		Status:        consts.MessageStatus_Sended, // 已发送	消息状态，1 发送中，2已发送，3已送达，4已读
		StatusTips:    consts.GetMsgStatusTips(consts.MessageStatus_Sended),
		ReadTimestamp: 0,
	}
	msgSendInfo := chat.MsgBasicInfo{
		//	消息基本信息
		MsgType: msg.MsgType,

		//	消息发送者信息
		SenderId:      msg.SenderId,
		SendTimestamp: tools.GtimeToTimestamp(msg.SendTime),
		SenderNick:    msg.SenderNick,
		SenderAvatar:  msg.SenderAvatar,

		//	信息内容
		MsgContentFmt:  msg.MsgContentFmt,
		MsgContentType: msg.MsgContentType,
		MsgContent:     chatInfo,

		//	接受者信息
		ConvId:   msg.ConvId,
		ConvType: msg.ReceiverType,
	}*/

	// 获取群成员
	var resGroupMeb []modelChat.ChatGroupMember
	result := dao.Db.Model(&modelChat.ChatGroupMember{}).Where("group_id = ?  AND deleted_at IS NULL AND exit_time IS NULL", msg.ReceiverId).Find(&resGroupMeb)
	if result.Error != nil {
		g.Log().Errorf(context.Background(), "获取群成员错误: %v", result.Error)
		return
	}

	groupMebIdList := make([]string, 0, len(resGroupMeb))
	for _, member := range resGroupMeb {
		groupMebIdList = append(groupMebIdList, member.UserId)
	}

	// 保存消息到数据库
	messageDb.saveGroupMessageToDatabase(msg, groupMebIdList)

	// 向所有在线成员发送消息
	for _, member := range resGroupMeb {
		// 跳过发送者自己
		if member.UserId == msg.SenderId {
			continue
		}

		// 检查成员是否在线
		globalWsData.connectionsMux.RLock()
		connUser, online := globalWsData.UserConnections[member.UserId]
		globalWsData.connectionsMux.RUnlock()

		if online {
			// 向该成员的所有登录设备发送消息
			for _, conn := range connUser {
				// 使用匿名函数确保锁的正确释放
				func(connection *UserConnection) {
					// 加锁保护WebSocket连接的写操作
					connection.WriteMux.Lock()
					defer connection.WriteMux.Unlock()

					// 检查连接是否有效
					select {
					case <-connection.Ctx.Done():
						// 连接已关闭，跳过发送
						return
					default:
						// 连接有效，继续处理
					}

					err := response.WsSuccess(connection.WsConn, "发送成功", msgSendInfo)
					if err != nil {
						g.Log().Errorf(context.Background(), "发送WebSocket消息错误: %v", err)
					} else {
						// 更新消息状态为已送达
						messageDb.updateMessageStatus(msg.MsgId, member.UserId, consts.MessageStatus_Delivered)
					}
				}(conn)
			}
		}
	}
}

// getTargetUsersByStrategy 根据推送策略获取目标用户列表
func (s *ServerChat) getTargetUsersByStrategy(msg *MessageItem) ([]string, error) {
	var targetUsers []string

	switch msg.PushStrategy {
	case 1: // 全部推送（默认）
		if msg.ReceiverType == 1 {
			// 单聊：发送方和接收方
			targetUsers = []string{msg.SenderId, msg.ReceiverId}
		} else {
			// 群聊：所有群成员
			members, err := s.getGroupMembers(msg.ReceiverId)
			if err != nil {
				return nil, err
			}
			targetUsers = members
		}

	case 2: // 仅发送方
		targetUsers = []string{msg.SenderId}

	case 3: // 仅接收方
		if msg.ReceiverType == 1 {
			// 单聊：仅接收方
			targetUsers = []string{msg.ReceiverId}
		} else {
			// 群聊：所有群成员（排除发送者）
			members, err := s.getGroupMembers(msg.ReceiverId)
			if err != nil {
				return nil, err
			}
			targetUsers = s.excludeUsers(members, []string{msg.SenderId})
		}

	case 4: // 指定用户
		targetUsers = msg.PushTargets

	case 5: // 群管理员（包括群主）
		if msg.ReceiverType == 2 {
			admins, err := s.getGroupAdmins(msg.ReceiverId)
			if err != nil {
				return nil, err
			}
			targetUsers = admins
		}

	case 6: // 群所有者
		if msg.ReceiverType == 2 {
			owner, err := s.getGroupOwner(msg.ReceiverId)
			if err != nil {
				return nil, err
			}
			if owner != "" {
				targetUsers = []string{owner}
			}
		}

	case 7: // 仅在线用户
		allTargets := []string{}
		if msg.ReceiverType == 1 {
			allTargets = []string{msg.SenderId, msg.ReceiverId}
		} else {
			members, err := s.getGroupMembers(msg.ReceiverId)
			if err != nil {
				return nil, err
			}
			allTargets = members
		}
		targetUsers = s.filterOnlineUsers(allTargets)

	case 9: // 排除发送者
		if msg.ReceiverType == 1 {
			targetUsers = []string{msg.ReceiverId}
		} else {
			members, err := s.getGroupMembers(msg.ReceiverId)
			if err != nil {
				return nil, err
			}
			targetUsers = s.excludeUsers(members, []string{msg.SenderId})
		}

	default:
		// 默认全部推送
		if msg.ReceiverType == 1 {
			targetUsers = []string{msg.SenderId, msg.ReceiverId}
		} else {
			members, err := s.getGroupMembers(msg.ReceiverId)
			if err != nil {
				return nil, err
			}
			targetUsers = members
		}
	}

	// 应用排除用户列表
	if len(msg.ExcludeUsers) > 0 {
		targetUsers = s.excludeUsers(targetUsers, msg.ExcludeUsers)
	}

	return targetUsers, nil
}

// getGroupMembers 获取群成员列表
func (s *ServerChat) getGroupMembers(groupId string) ([]string, error) {
	var members []string
	var groupMembers []modelChat.ChatGroupMember

	result := dao.Db.Model(&modelChat.ChatGroupMember{}).
		Where("group_id = ? AND deleted_at IS NULL AND exit_time IS NULL", groupId).
		Find(&groupMembers)

	if result.Error != nil {
		return nil, result.Error
	}

	for _, member := range groupMembers {
		members = append(members, member.UserId)
	}

	return members, nil
}

// getGroupAdmins 获取群管理员列表（包括群主）
func (s *ServerChat) getGroupAdmins(groupId string) ([]string, error) {
	var admins []string
	var groupMembers []modelChat.ChatGroupMember

	result := dao.Db.Model(&modelChat.ChatGroupMember{}).
		Where("group_id = ? AND role_type IN (1, 2) AND deleted_at IS NULL AND exit_time IS NULL", groupId).
		Find(&groupMembers)

	if result.Error != nil {
		return nil, result.Error
	}

	for _, member := range groupMembers {
		admins = append(admins, member.UserId)
	}

	return admins, nil
}

// getGroupOwner 获取群主
func (s *ServerChat) getGroupOwner(groupId string) (string, error) {
	var groupMember modelChat.ChatGroupMember

	result := dao.Db.Model(&modelChat.ChatGroupMember{}).
		Where("group_id = ? AND role_type = 1 AND deleted_at IS NULL AND exit_time IS NULL", groupId).
		First(&groupMember)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return "", nil
		}
		return "", result.Error
	}

	return groupMember.UserId, nil
}

// filterOnlineUsers 过滤在线用户
func (s *ServerChat) filterOnlineUsers(userIds []string) []string {
	var onlineUsers []string

	globalWsData.connectionsMux.RLock()
	defer globalWsData.connectionsMux.RUnlock()

	for _, userId := range userIds {
		if _, online := globalWsData.UserConnections[userId]; online {
			onlineUsers = append(onlineUsers, userId)
		}
	}

	return onlineUsers
}

// excludeUsers 从用户列表中排除指定用户
func (s *ServerChat) excludeUsers(userIds []string, excludeIds []string) []string {
	excludeMap := make(map[string]bool)
	for _, id := range excludeIds {
		excludeMap[id] = true
	}

	var result []string
	for _, userId := range userIds {
		if !excludeMap[userId] {
			result = append(result, userId)
		}
	}

	return result
}

// deliverPrivateMessageWithStrategy 根据策略推送单聊消息
func (s *ServerChat) deliverPrivateMessageWithStrategy(msg *MessageItem, targetUsers []string) {
	// 获取ws发送的消息
	msgSendInfo := s.msgItemToMsgBasicInfo(msg, consts.MessageStatus_Sended, false)

	// 保存消息到数据库
	messageDb.savePrivateMessageToDatabase(msg)

	// 向目标用户推送消息
	for _, userId := range targetUsers {
		// 检查用户是否在线
		globalWsData.connectionsMux.RLock()
		connUser, online := globalWsData.UserConnections[userId]
		globalWsData.connectionsMux.RUnlock()

		if online {
			// 判断是否是发送者本人
			isMyself := userId == msg.SenderId
			msgSendInfo := s.msgItemToMsgBasicInfo(msg, consts.MessageStatus_Sended, isMyself)

			// 向该用户的所有登录端发送消息
			for _, conn := range connUser {
				func(connection *UserConnection) {
					connection.WriteMux.Lock()
					defer connection.WriteMux.Unlock()

					err := response.WsSuccess(connection.WsConn, "发送成功", msgSendInfo)
					if err != nil {
						g.Log().Errorf(context.Background(), "发送WebSocket消息错误: %v", err)
					} else if !isMyself {
						// 更新消息状态为已送达（非发送者）
						messageDb.updateMessageStatus(msg.MsgId, userId, consts.MessageStatus_Delivered)
					}
				}(conn)
			}
		}
	}
}

// deliverGroupMessageWithStrategy 根据策略推送群聊消息
func (s *ServerChat) deliverGroupMessageWithStrategy(msg *MessageItem, targetUsers []string) {
	// 获取ws发送的消息
	msgSendInfo := s.msgItemToMsgBasicInfo(msg, consts.MessageStatus_Sended, false)

	// 保存消息到数据库
	messageDb.saveGroupMessageToDatabase(msg, targetUsers)

	// 向目标用户推送消息
	for _, userId := range targetUsers {
		// 跳过发送者自己（根据策略可能包含发送者）
		isMyself := userId == msg.SenderId
		if isMyself {
			msgSendInfo = s.msgItemToMsgBasicInfo(msg, consts.MessageStatus_Sended, true)
		} else {
			msgSendInfo = s.msgItemToMsgBasicInfo(msg, consts.MessageStatus_Sended, false)
		}

		// 检查成员是否在线
		globalWsData.connectionsMux.RLock()
		connUser, online := globalWsData.UserConnections[userId]
		globalWsData.connectionsMux.RUnlock()

		if online {
			// 向该成员的所有登录设备发送消息
			for _, conn := range connUser {
				func(connection *UserConnection) {
					connection.WriteMux.Lock()
					defer connection.WriteMux.Unlock()

					// 检查连接是否有效
					select {
					case <-connection.Ctx.Done():
						return
					default:
					}

					err := response.WsSuccess(connection.WsConn, "发送成功", msgSendInfo)
					if err != nil {
						g.Log().Errorf(context.Background(), "发送WebSocket消息错误: %v", err)
					} else if !isMyself {
						// 更新消息状态为已送达（非发送者）
						messageDb.updateMessageStatus(msg.MsgId, userId, consts.MessageStatus_Delivered)
					}
				}(conn)
			}
		}
	}
}

// CreateMessageWithStrategy 创建带推送策略的消息 - 便捷方法
func CreateMessageWithStrategy(msgType, senderId, receiverId string, receiverType int8, content string, strategy int8, targets []string) *MessageItem {
	return &MessageItem{
		MsgType:      msgType,
		MsgId:        uniqueId.GenerateMessageID(),
		MsgClientId:  "",
		ReceiverId:   receiverId,
		ReceiverType: receiverType,
		ConvId:       messageDb.generateConvId(receiverType, senderId, receiverId),

		MsgContentFmt:  1,
		MsgContentType: "text",
		MsgContent:     content,

		SenderId:     senderId,
		SenderNick:   "",
		SenderAvatar: "",
		SendTime:     gtime.Now(),

		PushStrategy: strategy,
		PushTargets:  targets,
		ExcludeUsers: []string{},
		Extra:        make(map[string]interface{}),
	}
}

// 处理已读回执
func (s *ServerChat) handleReadReceipt(conn *UserConnection, content interface{}) {
	// 转换消息内容
	/*_var readReq chat.MarkMessageReadReq
	err := gconv.Struct(content, &readReq)
	if err != nil {
		g.Log().Errorf(context.Background(), "转换已读回执内容错误: %v", err)
		return
	}

	// 创建上下文
	ctx := context.WithValue(context.Background(), "userId", conn.UserId)

	// 标记已读
	, err = s.MarkMessageRead(ctx, &readReq)
	if err != nil {
		// 发送错误响应
		conn.Conn.WriteJSON(WebSocketMessage{
			Type:    "error",
			Content: err.Error(),
		})
	}*/
}

// 心跳检测
func (s *ServerChat) heartbeatChecker() {
	ticker := time.NewTicker(time.Minute)
	defer ticker.Stop()

	for {
		<-ticker.C
		s.checkConnections()
	}
}

// 检查连接状态
func (s *ServerChat) checkConnections() {
	globalWsData.connectionsMux.RLock()
	defer globalWsData.connectionsMux.RUnlock()

	/*now := time.Now()
	for userId, conn := range globalWsData.UserConnections {
		// 如果超过5分钟没有心跳，关闭连接
		if now.Sub(conn.LastPing) > time.Minute*5 {
			g.Log().Infof(context.Background(), "用户 %s 心跳超时，关闭连接", userId)
			go s.unregisterConnection(userId)
		} else {
			// 发送ping消息
			conn.Conn.WriteMessage(websocket.PingMessage, []byte{})
		}
	}*/
}

// 处理消息队列
func (s *ServerChat) processMessageQueue() {
	for msg := range globalWsData.messageQueue {
		// 处理消息发送
		s.deliverMessage(msg)
	}
}

// 发送已读信息
func (s *ServerChat) SendMsgReadInfo(wsInfo chat.MsgBasicInfo, msgSendId string) {
	//	获取目标所有的会话
	var chatConvList []modelChat.ChatConversation

	result := dao.Db.Model(&modelChat.ChatConversation{}).Select("user_id").
		Where("conv_id = ?", wsInfo.ConvId).Find(&chatConvList)

	if result.Error == nil && len(chatConvList) > 0 {
		for _, chatConv := range chatConvList {
			s.sendInfoToUser(chatConv.UserId, wsInfo)
		}
	}
	return
}

// 处理单聊消息
func (s *ServerChat) sendInfoToUser(userId string, msgInfo interface{}) {
	// 检查接收者是否在线
	globalWsData.connectionsMux.RLock()
	connUser, online := globalWsData.UserConnections[userId]
	globalWsData.connectionsMux.RUnlock()

	// 如果在线，直接发送
	if online {
		//	同一个用户， 几个登录端都需要发送
		for _, conn := range connUser {
			var bIsSendOk = false
			func(_connection *UserConnection, _bIsSendOk *bool) {
				// 加锁保护WebSocket连接的写操作, 使用defer确保锁一定会被释放
				_connection.WriteMux.Lock()
				defer _connection.WriteMux.Unlock()

				_ = response.WsSuccess(_connection.WsConn, "发送成功", msgInfo)

			}(conn, &bIsSendOk)
		}
	}
}
